import React, { useRef, useEffect } from "react";
import { View, Animated, ScrollView, Image, Text, Dimensions, StyleSheet } from "react-native";

const ads = [
  {
    id: 1,
    image: require("@/assets/images/nike-shoe-black-bg.jpg"),
    title: "Az új NIKE HYPERFUSE cipője",
    desc: "már <PERSON> akár 20% kedvezménnyel elérhető a Nike online üzletében",
    logo: require("@/assets/images/logo.png"),
  },
  {
    id: 2,
    image: require("@/assets/images/laptop.jpg"),
    title: "Lecserélnéd már a régi laptopodat?",
    desc: "Itt a lehetőség, aknázd ki kedvezményeidet és szerezd be az új laptopodat!",
    logo: require("@/assets/images/logo.png"),
  },
];

const { width } = Dimensions.get("window");

export default function SpecialOfferSection() {
  const scrollX = useRef(new Animated.Value(0)).current;
  const adScrollRef = useRef<ScrollView>(null);

  useEffect(() => {
    let currentIndex = 0;
    const interval = setInterval(() => {
      currentIndex = (currentIndex + 1) % ads.length;
      adScrollRef.current?.scrollTo({ x: currentIndex * width, animated: true });
    }, 4000); // 4000ms for a 4-second interval
    return () => clearInterval(interval);
  }, []);

  return (
    <View style={styles.adCarouselWrapper}>
      <Animated.ScrollView
        ref={adScrollRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {ads.map((ad) => (
          <View key={ad.id} style={styles.adBox}>
            <Image source={ad.image} style={styles.adImage} />
            <View style={styles.adTextContainer}>
              <Text style={styles.adTitle}>{ad.title}</Text>
              <Text style={styles.adDesc}>{ad.desc}</Text>
            </View>
            <Image source={ad.logo} style={styles.adLogo} />
          </View>
        ))}
      </Animated.ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  adCarouselWrapper: {
    alignItems: "center",
    marginTop: 8,
    marginBottom: 0,
  },
  adBox: {
    width: width * 0.92,
    borderRadius: 24,
    marginHorizontal: width * 0.04,
    marginBottom: 8,
    overflow: "hidden",
    alignItems: "flex-start",
    justifyContent: "flex-end",
    padding: 0,
    height: 200,
  },
  adImage: {
    width: "100%",
    height: "100%",
    position: "absolute",
    borderRadius: 24,
  },
  adTextContainer: {
    position: "absolute",
    left: 18,
    top: 18,
    right: 18,
  },
  adTitle: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "bold",
    marginBottom: 4,
  },
  adDesc: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "400",
  },
  adLogo: {
    width: 80,
    height: 28,
    resizeMode: "contain",
    position: "absolute",
    right: 18,
    bottom: 18,
  },
});
