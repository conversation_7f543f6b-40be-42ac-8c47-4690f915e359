import { View, TouchableOpacity, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter, usePathname } from "expo-router";
import React, { useState, useEffect } from "react";
import { ThemeContextProvider, useTheme } from "@/components/ThemeContext";

export default function MainTabs() {
  const router = useRouter();
  const pathname = usePathname();
  const [selectedTab, setSelectedTab] = useState("home");

  // Update selected tab based on current route
  useEffect(() => {
    if (pathname === "/home") setSelectedTab("home");
    else if (pathname === "/favs") setSelectedTab("favs");
    else if (pathname === "/map") setSelectedTab("map");
    else if (pathname === "/profile") setSelectedTab("profile");
  }, [pathname]);

  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  return (
    <View style={[styles.tabContainer, { backgroundColor: theme.tertiary }]}>
      <TouchableOpacity onPress={() => router.replace("/home")}>
        <Ionicons name="home-outline" size={28} color={selectedTab === "home" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>
      <TouchableOpacity onPress={() => router.replace("/favs")}>
        <Ionicons name="heart-outline" size={28} color={selectedTab === "favs" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>
      <TouchableOpacity onPress={() => router.replace("/map")}>
        <Ionicons name="map-outline" size={28} color={selectedTab === "map" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>
      <TouchableOpacity onPress={() => router.replace("/profile")}>
        <Ionicons name="person-outline" size={28} color={selectedTab === "profile" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>

    </View>
  );
}
const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    padding: 10,
  },
});