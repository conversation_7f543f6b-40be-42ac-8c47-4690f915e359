import { View, TouchableOpacity, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import { ThemeContextProvider, useTheme } from "@/components/ThemeContext";

export default function MainTabs() {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState("home");

  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  return (
    <View style={[styles.tabContainer, { backgroundColor: theme.tertiary }]}>
      <TouchableOpacity onPress={() => { setSelectedTab("home"); router.push("/home"); }}>
        <Ionicons name="home-outline" size={28} color={selectedTab === "home" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>
      <TouchableOpacity onPress={() => { setSelectedTab("favs"); router.push("/favs"); }}>
        <Ionicons name="heart-outline" size={28} color={selectedTab === "favs" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>
      <TouchableOpacity onPress={() => { setSelectedTab("map"); router.push("/map"); }}>
        <Ionicons name="map-outline" size={28} color={selectedTab === "map" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>
      <TouchableOpacity onPress={() => { setSelectedTab("profile"); router.push("/profile"); }}>
        <Ionicons name="person-outline" size={28} color={selectedTab === "profile" ? "#40E0D0" : "gray"} />
      </TouchableOpacity>
      
    </View>
  );
}
const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    padding: 10,
  },
});