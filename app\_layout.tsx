import { DefaultTheme, ThemeProvider } from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { Ionicons } from "@expo/vector-icons";
import { useEffect } from "react";
import MainTabs from "@/components/MainTabs";
import { FavouritesProvider } from "@/components/FavouritesContext";
import { SafeAreaView } from "react-native-safe-area-context";
import { ThemeContextProvider, useTheme } from "@/components/ThemeContext";
import { AuthProvider } from '@/components/AuthContext';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });
  
  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <ThemeContextProvider>
      <InnerLayout />
    </ThemeContextProvider>
  );
}

// Separate inner layout so we can use the context hook inside the provider
function InnerLayout() {
  const { getNavigationTheme } = useTheme();

  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  return (
    <AuthProvider>
      <FavouritesProvider>
        <SafeAreaView style={{ flex: 1, backgroundColor: theme.tertiary }}>
          <ThemeProvider value={getNavigationTheme()}>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="index" />
              <Stack.Screen name="home/index" options={{ headerShown: false }} />
              {/* Other screens can go here */}
            </Stack>
            <MainTabs />
          </ThemeProvider>
        </SafeAreaView>
      </FavouritesProvider>
    </AuthProvider>
  );
}